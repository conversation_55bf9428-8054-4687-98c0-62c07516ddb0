#!/usr/bin/env python3
"""
Debug script to understand the exact data structure of .pt files
"""

import torch
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

def examine_data_structure():
    """Examine the structure of .pt files in detail."""
    
    # Load a few test files
    test_dir = Path("/home/<USER>/ma_yugi/data/07_gnn_ready/test")
    
    # Check temporal_3 and temporal_5 directories
    for temporal_window in [3, 5]:
        temporal_dir = test_dir / f"temporal_{temporal_window}"
        if temporal_dir.exists():
            print(f"\n{'='*60}")
            print(f"EXAMINING TEMPORAL WINDOW {temporal_window}")
            print(f"{'='*60}")
            
            # Get first few files
            pt_files = list(temporal_dir.glob("*.pt"))[:3]
            
            for i, pt_file in enumerate(pt_files):
                print(f"\n--- File {i+1}: {pt_file.name} ---")
                
                try:
                    # Load the data
                    data = torch.load(pt_file, weights_only=False)
                    
                    print(f"Data type: {type(data)}")
                    print(f"Data attributes: {dir(data)}")
                    
                    # Examine each attribute
                    if hasattr(data, 'x'):
                        print(f"\nNode features (x):")
                        print(f"  Shape: {data.x.shape}")
                        print(f"  Dtype: {data.x.dtype}")
                        print(f"  Min/Max: {data.x.min():.4f} / {data.x.max():.4f}")
                        print(f"  First 3 nodes, first 10 features:")
                        print(data.x[:3, :10])
                        
                    if hasattr(data, 'pos'):
                        print(f"\nNode positions (pos):")
                        print(f"  Shape: {data.pos.shape}")
                        print(f"  Dtype: {data.pos.dtype}")
                        print(f"  Min/Max per dim:")
                        for dim in range(data.pos.shape[1]):
                            print(f"    Dim {dim}: {data.pos[:, dim].min():.4f} / {data.pos[:, dim].max():.4f}")
                        print(f"  First 5 positions:")
                        print(data.pos[:5])
                        
                    if hasattr(data, 'y'):
                        print(f"\nNode labels (y):")
                        print(f"  Shape: {data.y.shape}")
                        print(f"  Dtype: {data.y.dtype}")
                        print(f"  Unique values: {torch.unique(data.y)}")
                        print(f"  Value counts:")
                        for val in torch.unique(data.y):
                            count = (data.y == val).sum()
                            print(f"    {val}: {count} nodes")
                            
                    if hasattr(data, 'edge_index'):
                        print(f"\nEdge indices (edge_index):")
                        print(f"  Shape: {data.edge_index.shape}")
                        print(f"  Dtype: {data.edge_index.dtype}")
                        print(f"  Num edges: {data.edge_index.shape[1]}")
                        print(f"  Edge range: {data.edge_index.min()} to {data.edge_index.max()}")
                        
                    # Check for additional attributes
                    other_attrs = [attr for attr in dir(data) if not attr.startswith('_') and attr not in ['x', 'pos', 'y', 'edge_index']]
                    if other_attrs:
                        print(f"\nOther attributes: {other_attrs}")
                        for attr in other_attrs:
                            try:
                                val = getattr(data, attr)
                                if torch.is_tensor(val):
                                    print(f"  {attr}: tensor shape {val.shape}, dtype {val.dtype}")
                                else:
                                    print(f"  {attr}: {type(val)} = {val}")
                            except:
                                print(f"  {attr}: <could not access>")
                                
                except Exception as e:
                    print(f"Error loading {pt_file}: {e}")
                    
                if i == 0:  # Only detailed analysis for first file
                    break

def analyze_feature_structure():
    """Analyze the structure of node features in detail."""
    
    print(f"\n{'='*60}")
    print("DETAILED FEATURE ANALYSIS")
    print(f"{'='*60}")
    
    # Load one file from each temporal window
    test_dir = Path("/home/<USER>/ma_yugi/data/07_gnn_ready/test")
    
    for temporal_window in [3, 5]:
        temporal_dir = test_dir / f"temporal_{temporal_window}"
        if temporal_dir.exists():
            pt_files = list(temporal_dir.glob("*.pt"))
            if pt_files:
                print(f"\n--- TEMPORAL WINDOW {temporal_window} ---")
                
                data = torch.load(pt_files[0], weights_only=False)
                
                print(f"Feature dimensions: {data.x.shape[1]}")
                
                # Analyze each feature dimension
                for dim in range(data.x.shape[1]):
                    feature_values = data.x[:, dim]
                    print(f"Feature {dim:2d}: min={feature_values.min():8.4f}, max={feature_values.max():8.4f}, "
                          f"mean={feature_values.mean():8.4f}, std={feature_values.std():8.4f}")
                
                # Check if positions are encoded in features
                print(f"\nPosition analysis:")
                print(f"Pos shape: {data.pos.shape}")
                print(f"Pos ranges:")
                for dim in range(data.pos.shape[1]):
                    pos_values = data.pos[:, dim]
                    print(f"  Pos dim {dim}: min={pos_values.min():8.4f}, max={pos_values.max():8.4f}")
                
                # Look for correlations between features and positions
                print(f"\nLooking for position correlations in features:")
                for pos_dim in range(data.pos.shape[1]):
                    pos_values = data.pos[:, pos_dim]
                    for feat_dim in range(min(10, data.x.shape[1])):  # Check first 10 features
                        feat_values = data.x[:, feat_dim]
                        correlation = torch.corrcoef(torch.stack([pos_values, feat_values]))[0, 1]
                        if abs(correlation) > 0.9:
                            print(f"  High correlation ({correlation:.4f}) between pos[{pos_dim}] and feature[{feat_dim}]")

if __name__ == "__main__":
    examine_data_structure()
    analyze_feature_structure()
