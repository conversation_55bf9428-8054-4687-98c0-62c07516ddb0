accuracy: 0.6078761359811511
explained_variance: 0.06989669799804688
f1: 0.6633920832129442
mae: 0.47341278195381165
max_error: !!python/object/apply:numpy._core.multiarray.scalar
- &id001 !!python/object/apply:numpy.dtype
  args:
  - f4
  - false
  - true
  state: !!python/tuple
  - 3
  - <
  - null
  - null
  - null
  - -1
  - -1
  - 0
- !!binary |
  rcN9Pw==
median_ae: !!python/object/apply:numpy._core.multiarray.scalar
- *id001
- !!binary |
  2u74Pg==
mse: 0.23141711950302124
precision: 0.5448504983388704
r2_score: 0.06702053546905518
recall: 0.8478581979320532
rmse: !!python/object/apply:numpy._core.multiarray.scalar
- &id002 !!python/object/apply:numpy.dtype
  args:
  - f8
  - false
  - true
  state: !!python/tuple
  - 3
  - <
  - null
  - null
  - null
  - -1
  - -1
  - 0
- !!binary |
  1c6m46jJ3j8=
roc_auc: !!python/object/apply:numpy._core.multiarray.scalar
- *id002
- !!binary |
  B0uGi1Q05T8=
