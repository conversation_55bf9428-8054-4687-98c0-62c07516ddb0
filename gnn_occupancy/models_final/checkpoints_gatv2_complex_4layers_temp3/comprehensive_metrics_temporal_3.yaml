accuracy: 0.7283742847526086
explained_variance: 0.24633938074111938
f1: 0.6958160572936298
mae: 0.395372211933136
mae_logits: 0.9411689043045044
max_error: !!python/object/apply:numpy.core.multiarray.scalar
- &id001 !!python/object/apply:numpy.dtype
  args:
  - f4
  - false
  - true
  state: !!python/tuple
  - 3
  - <
  - null
  - null
  - null
  - -1
  - -1
  - 0
- !!binary |
  duVkPw==
mean_prob: !!python/object/apply:numpy.core.multiarray.scalar
- *id001
- !!binary |
  I4vZPg==
mean_target: !!python/object/apply:numpy.core.multiarray.scalar
- *id001
- !!binary |
  mVbpPg==
median_ae: !!python/object/apply:numpy.core.multiarray.scalar
- *id001
- !!binary |
  tGrBPg==
mse: 0.18789036571979523
mse_logits: 1.3169102668762207
precision: 0.7105465742879138
r2_logits: -4.309245586395264
r2_score: 0.2425026297569275
recall: 0.6816838995568686
rmse: !!python/object/apply:numpy.core.multiarray.scalar
- &id002 !!python/object/apply:numpy.dtype
  args:
  - f8
  - false
  - true
  state: !!python/tuple
  - 3
  - <
  - null
  - null
  - null
  - -1
  - -1
  - 0
- !!binary |
  PsO7h9y92z8=
roc_auc: !!python/object/apply:numpy.core.multiarray.scalar
- *id002
- !!binary |
  sEGeRo6T6T8=
std_prob: !!python/object/apply:numpy.core.multiarray.scalar
- *id001
- !!binary |
  QKREPg==
std_target: !!python/object/apply:numpy.core.multiarray.scalar
- *id001
- !!binary |
  uP7+Pg==
