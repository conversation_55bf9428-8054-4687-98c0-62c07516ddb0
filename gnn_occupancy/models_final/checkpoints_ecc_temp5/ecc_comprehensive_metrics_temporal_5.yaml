accuracy: 0.6520418494768815
explained_variance: 0.1371360421180725
f1: 0.6675266043211867
mae: 0.44151440262794495
max_error: !!python/object/apply:numpy._core.multiarray.scalar
- &id001 !!python/object/apply:numpy.dtype
  args:
  - f4
  - false
  - true
  state: !!python/tuple
  - 3
  - <
  - null
  - null
  - null
  - -1
  - -1
  - 0
- !!binary |
  RwZdPw==
median_ae: !!python/object/apply:numpy._core.multiarray.scalar
- *id001
- !!binary |
  ssnhPg==
mse: 0.21572668850421906
precision: 0.5961981566820277
r2_score: 0.13172411918640137
recall: 0.7582417582417582
rmse: !!python/object/apply:numpy._core.multiarray.scalar
- &id002 !!python/object/apply:numpy.dtype
  args:
  - f8
  - false
  - true
  state: !!python/tuple
  - 3
  - <
  - null
  - null
  - null
  - -1
  - -1
  - 0
- !!binary |
  0E47rca53T8=
roc_auc: !!python/object/apply:numpy._core.multiarray.scalar
- *id002
- !!binary |
  +mDsl+7m5j8=
