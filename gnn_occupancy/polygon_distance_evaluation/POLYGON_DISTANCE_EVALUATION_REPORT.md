# Polygon-Based Distance Evaluation Report

**Generated:** 2025-06-06 09:54:40

**Models Evaluated:** 8

## Executive Summary

- **Best Traditional Accuracy:** gatv2_3L_64H_temp3_checkpoints_gatv2_temp3 (0.7448)
- **Best Distance Accuracy:** ecc_3L_64H_temp5_checkpoints_ecc_temp5 (0.0000m)
- **Best 20cm Tolerance:** ecc_3L_64H_temp3_checkpoints_ecc_temp3 (0.0541)

## Key Findings

### Spatial Accuracy Insights

1. **Distance-based evaluation provides more nuanced insights** into model performance for spatial applications compared to traditional IoU metrics.

2. **Tolerance-based accuracy** shows how models perform at different precision requirements for collaborative robotics.

3. **Architecture differences** are more pronounced when evaluated using spatial distance metrics.

## Detailed Results

### Traditional Classification Metrics

| Model                                                       |   Accuracy |   Precision |   Recall |   F1_Score |   ROC_AUC |
|:------------------------------------------------------------|-----------:|------------:|---------:|-----------:|----------:|
| ecc_3L_64H_temp5_checkpoints_ecc_temp5                      |   0        |    0        | 0        |   0        |         0 |
| gatv2_3L_64H_temp3_checkpoints_gatv2_temp3                  |   0.744793 |    0.713622 | 0.907747 |   0.799063 |         0 |
| ecc_hybrid_3L_64H_temp3_checkpoints_ecc_hybrid_temp3        |   0        |    0        | 0        |   0        |         0 |
| gatv2_3L_64H_temp3_checkpoints                              |   0.744793 |    0.713622 | 0.907747 |   0.799063 |         0 |
| ecc_3L_64H_temp3_checkpoints_ecc_temp3                      |   0.716033 |    0.704523 | 0.847428 |   0.769396 |         0 |
| gatv2_4L_128H_temp3_checkpoints_gatv2_complex_4layers_temp3 |   0.734876 |    0.755607 | 0.777055 |   0.766181 |         0 |
| gatv2_3L_64H_temp5_checkpoints_temp5                        |   0.688157 |    0.675843 | 0.851983 |   0.753759 |         0 |
| gatv2_5L_64H_temp3_checkpoints_gatv2_5layers_temp3          |   0.690909 |    0.653659 | 0.950917 |   0.774753 |         0 |

### Distance-Based Metrics

| Model                                                       |   Mean_Distance_Error |   Median_Distance_Error |   P90_Distance |   P95_Distance |
|:------------------------------------------------------------|----------------------:|------------------------:|---------------:|---------------:|
| ecc_3L_64H_temp5_checkpoints_ecc_temp5                      |               0       |                 0       |        0       |        0       |
| gatv2_3L_64H_temp3_checkpoints_gatv2_temp3                  |               2.99996 |                 2.81602 |        5.45112 |        5.66485 |
| ecc_hybrid_3L_64H_temp3_checkpoints_ecc_hybrid_temp3        |               0       |                 0       |        0       |        0       |
| gatv2_3L_64H_temp3_checkpoints                              |               2.99996 |                 2.81602 |        5.45112 |        5.66485 |
| ecc_3L_64H_temp3_checkpoints_ecc_temp3                      |               2.72115 |                 2.64465 |        5.44815 |        5.64258 |
| gatv2_4L_128H_temp3_checkpoints_gatv2_complex_4layers_temp3 |               3.05277 |                 2.82631 |        5.51483 |        5.75386 |
| gatv2_3L_64H_temp5_checkpoints_temp5                        |               3.01808 |                 2.83273 |        5.44994 |        5.66336 |
| gatv2_5L_64H_temp3_checkpoints_gatv2_5layers_temp3          |               2.80466 |                 2.70161 |        5.16241 |        5.62427 |

### Tolerance-Based Accuracy

| Model                                                       |   Within_15cm |   Within_20cm |   Within_25cm |
|:------------------------------------------------------------|--------------:|--------------:|--------------:|
| ecc_3L_64H_temp5_checkpoints_ecc_temp5                      |     0         |     0         |     0         |
| gatv2_3L_64H_temp3_checkpoints_gatv2_temp3                  |     0.0283589 |     0.0395165 |     0.0478847 |
| ecc_hybrid_3L_64H_temp3_checkpoints_ecc_hybrid_temp3        |     0         |     0         |     0         |
| gatv2_3L_64H_temp3_checkpoints                              |     0.0283589 |     0.0395165 |     0.0478847 |
| ecc_3L_64H_temp3_checkpoints_ecc_temp3                      |     0.0368732 |     0.0540806 |     0.0771878 |
| gatv2_4L_128H_temp3_checkpoints_gatv2_complex_4layers_temp3 |     0.0310523 |     0.0477286 |     0.0707303 |
| gatv2_3L_64H_temp5_checkpoints_temp5                        |     0.0266854 |     0.0396067 |     0.0587079 |
| gatv2_5L_64H_temp3_checkpoints_gatv2_5layers_temp3          |     0.0308943 |     0.0455285 |     0.0650407 |

## Architecture Analysis

| Architecture   |   ('Mean_Distance_Error', 'mean') |   ('Mean_Distance_Error', 'std') |   ('Accuracy', 'mean') |   ('Accuracy', 'std') |   ('Within_20cm', 'mean') |   ('Within_20cm', 'std') |
|:---------------|----------------------------------:|---------------------------------:|-----------------------:|----------------------:|--------------------------:|-------------------------:|
| ecc            |                            0.907  |                           1.5711 |                 0.2387 |                0.4134 |                    0.018  |                   0.0312 |
| gatv2          |                            2.9751 |                           0.0977 |                 0.7207 |                0.0288 |                    0.0424 |                   0.004  |

## Temporal Window Analysis

| Temporal_Window   |   ('Mean_Distance_Error', 'mean') |   ('Mean_Distance_Error', 'std') |   ('Accuracy', 'mean') |   ('Accuracy', 'std') |   ('Within_20cm', 'mean') |   ('Within_20cm', 'std') |
|:------------------|----------------------------------:|---------------------------------:|-----------------------:|----------------------:|--------------------------:|-------------------------:|
| T3                |                            2.4298 |                           1.1973 |                 0.6052 |                0.2972 |                    0.0377 |                   0.0193 |
| T5                |                            1.509  |                           2.1341 |                 0.3441 |                0.4866 |                    0.0198 |                   0.028  |

## Recommendations for Collaborative Robotics

1. **For High-Precision Applications (< 15cm tolerance):** Use models with lowest mean distance error

2. **For Standard Applications (< 20cm tolerance):** Balance between distance accuracy and computational efficiency

3. **For Robust Applications (< 25cm tolerance):** Consider models with highest tolerance-based accuracy

