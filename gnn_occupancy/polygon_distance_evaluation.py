#!/usr/bin/env python3
"""
Comprehensive Polygon-Based Distance Evaluation System for GNN Occupancy Prediction

This system implements enhanced spatial accuracy evaluation by:
1. Converting GNN predictions back to spatial coordinates
2. Generating dynamic polygon ground truth boundaries
3. Calculating distance-based accuracy metrics
4. Providing comprehensive model comparison and analysis

Author: AI Assistant
Date: 2024
"""

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from tqdm import tqdm
import yaml
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Geometric computation libraries
from shapely.geometry import Point, Polygon, LineString
from shapely.ops import unary_union
from scipy.spatial.distance import cdist
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score

# Import existing modules
from data import create_data_loaders
from model import create_model
from utils import set_seed, get_device


@dataclass
class ModelInfo:
    """Information about a trained model."""
    name: str
    checkpoint_path: str
    config_path: str
    temporal_window: int
    architecture: str
    description: str


@dataclass
class SpatialMetrics:
    """Spatial accuracy metrics for distance-based evaluation."""
    # Traditional metrics
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    roc_auc: float
    
    # Distance-based metrics
    mean_distance_error: float
    median_distance_error: float
    std_distance_error: float
    percentile_90_distance: float
    percentile_95_distance: float
    percentile_99_distance: float
    
    # Tolerance-based accuracy
    within_15cm_accuracy: float
    within_20cm_accuracy: float
    within_25cm_accuracy: float
    
    # Object-specific metrics
    workstation_distance_error: float
    robot_distance_error: float
    boundary_distance_error: float
    
    # Spatial precision metrics
    spatial_precision: float
    spatial_recall: float
    
    # Sample counts
    total_predictions: int
    occupied_predictions: int
    unoccupied_predictions: int


@dataclass
class ArenaSpecifications:
    """Arena and object specifications for polygon generation."""
    # Arena dimensions
    arena_width: float = 21.06  # meters
    arena_height: float = 11.81  # meters
    arena_bounds: Tuple[float, float, float, float] = (-10.39, 10.67, -5.31, 6.50)  # (min_x, max_x, min_y, max_y)
    
    # Grid specifications
    voxel_size: float = 0.1  # meters
    grid_width: int = 211
    grid_height: int = 119
    
    # Object dimensions
    workstation_width: float = 1.0  # meters
    workstation_height: float = 0.65  # meters
    robot_width: float = 0.32  # meters
    robot_height: float = 0.24  # meters
    
    # Workstation configurations
    workstation_positions: Dict[str, Tuple[float, float, float]] = None  # (x, y, rotation)
    
    def __post_init__(self):
        """Initialize default workstation positions if not provided."""
        if self.workstation_positions is None:
            # Default workstation positions (these would need to be extracted from data)
            self.workstation_positions = {
                'AS_1': (0.0, 0.0, 0.0),  # Horizontal
                'AS_2': (2.0, 0.0, 0.0),  # Horizontal  
                'AS_3': (4.0, 0.0, 0.0),  # Horizontal
                'AS_4': (6.0, 0.0, 90.0),  # Vertical
                'AS_5': (8.0, 0.0, 0.0),  # Horizontal
            }


class SpatialCoordinateMapper:
    """Handles conversion between graph nodes and spatial coordinates."""
    
    def __init__(self, arena_specs: ArenaSpecifications):
        """Initialize the spatial coordinate mapper."""
        self.arena_specs = arena_specs
        
    def node_features_to_coordinates(self, node_features: torch.Tensor) -> np.ndarray:
        """
        Convert node features to spatial coordinates.
        
        Args:
            node_features: Node feature tensor [N, feature_dim]
            
        Returns:
            Spatial coordinates [N, 3] (x, y, z)
        """
        # Extract spatial coordinates from features (dimensions 3, 4, 5)
        if node_features.shape[1] >= 6:
            coordinates = node_features[:, 3:6].cpu().numpy()
        else:
            raise ValueError(f"Insufficient features for coordinate extraction: {node_features.shape[1]}")
            
        return coordinates
    
    def coordinates_to_grid_indices(self, coordinates: np.ndarray) -> np.ndarray:
        """
        Convert spatial coordinates to grid indices.
        
        Args:
            coordinates: Spatial coordinates [N, 3]
            
        Returns:
            Grid indices [N, 2] (grid_x, grid_y)
        """
        min_x, max_x, min_y, max_y = self.arena_specs.arena_bounds
        
        # Convert to grid coordinates
        grid_x = ((coordinates[:, 0] - min_x) / self.arena_specs.voxel_size).astype(int)
        grid_y = ((coordinates[:, 1] - min_y) / self.arena_specs.voxel_size).astype(int)
        
        # Clamp to valid grid bounds
        grid_x = np.clip(grid_x, 0, self.arena_specs.grid_width - 1)
        grid_y = np.clip(grid_y, 0, self.arena_specs.grid_height - 1)
        
        return np.column_stack([grid_x, grid_y])
    
    def grid_indices_to_coordinates(self, grid_indices: np.ndarray) -> np.ndarray:
        """
        Convert grid indices back to spatial coordinates.
        
        Args:
            grid_indices: Grid indices [N, 2]
            
        Returns:
            Spatial coordinates [N, 2] (x, y)
        """
        min_x, max_x, min_y, max_y = self.arena_specs.arena_bounds
        
        # Convert grid indices to spatial coordinates (center of voxel)
        x = min_x + (grid_indices[:, 0] + 0.5) * self.arena_specs.voxel_size
        y = min_y + (grid_indices[:, 1] + 0.5) * self.arena_specs.voxel_size
        
        return np.column_stack([x, y])


class DynamicPolygonGenerator:
    """Generates ground truth polygons for workstations, robots, and boundaries."""
    
    def __init__(self, arena_specs: ArenaSpecifications):
        """Initialize the polygon generator."""
        self.arena_specs = arena_specs
        
    def extract_robot_poses_from_data(self, data) -> List[Tuple[float, float, float]]:
        """
        Extract robot positions and orientations from graph data.
        
        Args:
            data: PyTorch Geometric Data object
            
        Returns:
            List of (x, y, orientation) tuples for robots
        """
        robot_poses = []
        
        # Find nodes labeled as robots (label 2 in original, 1 in binary)
        if hasattr(data, 'y'):
            # Get original labels if available, otherwise use binary labels
            labels = data.y.cpu().numpy()
            coordinates = data.pos.cpu().numpy()
            
            # Find robot nodes (assuming binary labels where 1 = occupied)
            # We need to identify which occupied nodes are robots vs workstations
            # This is a simplification - in practice, we'd need the original 5-class labels
            occupied_mask = labels == 1
            occupied_coords = coordinates[occupied_mask]
            
            # For now, assume all occupied points could be robots
            # In a real implementation, we'd distinguish between workstations and robots
            for coord in occupied_coords:
                # Default orientation (would need to be extracted from temporal data)
                robot_poses.append((coord[0], coord[1], 0.0))
                
        return robot_poses
    
    def create_workstation_polygons(self) -> List[Polygon]:
        """
        Create polygons for all workstations.
        
        Returns:
            List of workstation polygons
        """
        workstation_polygons = []
        
        for ws_name, (x, y, rotation) in self.arena_specs.workstation_positions.items():
            polygon = self._create_rotated_rectangle(
                x, y, 
                self.arena_specs.workstation_width,
                self.arena_specs.workstation_height,
                rotation
            )
            workstation_polygons.append(polygon)
            
        return workstation_polygons
    
    def create_robot_polygons(self, robot_poses: List[Tuple[float, float, float]]) -> List[Polygon]:
        """
        Create polygons for robots at given poses.
        
        Args:
            robot_poses: List of (x, y, orientation) tuples
            
        Returns:
            List of robot polygons
        """
        robot_polygons = []
        
        for x, y, orientation in robot_poses:
            polygon = self._create_rotated_rectangle(
                x, y,
                self.arena_specs.robot_width,
                self.arena_specs.robot_height,
                orientation
            )
            robot_polygons.append(polygon)
            
        return robot_polygons
    
    def create_boundary_polygons(self) -> List[Polygon]:
        """
        Create polygons for arena boundaries.
        
        Returns:
            List of boundary polygons
        """
        min_x, max_x, min_y, max_y = self.arena_specs.arena_bounds
        
        # Create boundary as the arena perimeter
        arena_boundary = Polygon([
            (min_x, min_y),
            (max_x, min_y),
            (max_x, max_y),
            (min_x, max_y)
        ])
        
        return [arena_boundary]
    
    def _create_rotated_rectangle(self, center_x: float, center_y: float, 
                                width: float, height: float, rotation_deg: float) -> Polygon:
        """
        Create a rotated rectangle polygon.
        
        Args:
            center_x, center_y: Center coordinates
            width, height: Rectangle dimensions
            rotation_deg: Rotation in degrees
            
        Returns:
            Rotated rectangle polygon
        """
        # Create rectangle corners relative to center
        half_w, half_h = width / 2, height / 2
        corners = np.array([
            [-half_w, -half_h],
            [half_w, -half_h],
            [half_w, half_h],
            [-half_w, half_h]
        ])
        
        # Apply rotation
        rotation_rad = np.radians(rotation_deg)
        cos_r, sin_r = np.cos(rotation_rad), np.sin(rotation_rad)
        rotation_matrix = np.array([[cos_r, -sin_r], [sin_r, cos_r]])
        
        rotated_corners = corners @ rotation_matrix.T
        
        # Translate to center position
        final_corners = rotated_corners + np.array([center_x, center_y])
        
        return Polygon(final_corners)


class DistanceCalculator:
    """Calculates distance-based metrics between predictions and polygon boundaries."""

    def __init__(self):
        """Initialize the distance calculator."""
        pass

    def point_to_polygon_distance(self, points: np.ndarray, polygons: List[Polygon]) -> np.ndarray:
        """
        Calculate minimum distance from points to polygon boundaries.

        Args:
            points: Array of points [N, 2]
            polygons: List of polygons

        Returns:
            Minimum distances [N]
        """
        if not polygons:
            return np.full(len(points), np.inf)

        min_distances = np.full(len(points), np.inf)

        for polygon in polygons:
            # Get polygon boundary
            boundary = polygon.boundary

            # Calculate distance from each point to this polygon boundary
            for i, point in enumerate(points):
                point_geom = Point(point)
                distance = point_geom.distance(boundary)
                min_distances[i] = min(min_distances[i], distance)

        return min_distances

    def calculate_tolerance_accuracy(self, distances: np.ndarray, tolerance: float) -> float:
        """
        Calculate accuracy within a distance tolerance.

        Args:
            distances: Array of distances
            tolerance: Distance tolerance in meters

        Returns:
            Accuracy within tolerance
        """
        if len(distances) == 0:
            return 0.0
        return np.mean(distances <= tolerance)

    def calculate_distance_statistics(self, distances: np.ndarray) -> Dict[str, float]:
        """
        Calculate comprehensive distance statistics.

        Args:
            distances: Array of distances

        Returns:
            Dictionary of distance statistics
        """
        if len(distances) == 0:
            return {
                'mean': 0.0, 'median': 0.0, 'std': 0.0,
                'percentile_90': 0.0, 'percentile_95': 0.0, 'percentile_99': 0.0
            }

        return {
            'mean': float(np.mean(distances)),
            'median': float(np.median(distances)),
            'std': float(np.std(distances)),
            'percentile_90': float(np.percentile(distances, 90)),
            'percentile_95': float(np.percentile(distances, 95)),
            'percentile_99': float(np.percentile(distances, 99))
        }


class ModelEvaluator:
    """Evaluates GNN models using polygon-based distance metrics."""

    def __init__(self, models_dir: str = "models_final", output_dir: str = "polygon_distance_evaluation"):
        """Initialize the model evaluator."""
        self.models_dir = Path(models_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # Initialize components
        self.arena_specs = ArenaSpecifications()
        self.coordinate_mapper = SpatialCoordinateMapper(self.arena_specs)
        self.polygon_generator = DynamicPolygonGenerator(self.arena_specs)
        self.distance_calculator = DistanceCalculator()

        # Device setup
        self.device = get_device()
        set_seed(42)

        # Discover available models
        self.models = self._discover_models()

    def _discover_models(self) -> List[ModelInfo]:
        """Discover all available trained models."""
        models = []

        model_configs = [
            ("GATv2_Complex_T3", "checkpoints_gatv2_complex_4layers_temp3", 3, "GATv2", "Complex GATv2 with 4 layers, temporal window 3"),
            ("GATv2_Complex_T5", "checkpoints_gatv2_complex_temp5", 5, "GATv2", "Complex GATv2 with temporal window 5"),
            ("GATv2_Standard_T3", "checkpoints_gatv2_temp3", 3, "GATv2", "Standard GATv2 with temporal window 3"),
            ("GATv2_Standard_T5", "checkpoints_temp5", 5, "GATv2", "Standard GATv2 with temporal window 5"),
            ("ECC_T3", "checkpoints_ecc_temp3", 3, "ECC", "ECC model with temporal window 3"),
            ("ECC_T5", "checkpoints_ecc_temp5", 5, "ECC", "ECC model with temporal window 5"),
            ("Enhanced_GATv2_T3", "checkpoints_enhanced_temp3", 3, "Enhanced_GATv2", "Enhanced GATv2 with temporal window 3"),
        ]

        for name, checkpoint_dir, temporal_window, architecture, description in model_configs:
            checkpoint_path = self.models_dir / checkpoint_dir / f"model_temporal_{temporal_window}_best.pt"
            config_path = self.models_dir / checkpoint_dir / "config.yaml"

            if checkpoint_path.exists():
                models.append(ModelInfo(
                    name=name,
                    checkpoint_path=str(checkpoint_path),
                    config_path=str(config_path) if config_path.exists() else None,
                    temporal_window=temporal_window,
                    architecture=architecture,
                    description=description
                ))
                print(f"✅ Found model: {name}")
            else:
                print(f"❌ Model not found: {name} at {checkpoint_path}")

        return models

    def load_model_config(self, model_info: ModelInfo) -> Dict:
        """Load model configuration."""
        # Handle ECC models specially
        if model_info.architecture.lower() == "ecc":
            config = self._create_ecc_config(Path(model_info.checkpoint_path).parent, model_info.temporal_window)
        elif model_info.config_path and Path(model_info.config_path).exists():
            with open(model_info.config_path, 'r') as f:
                config = yaml.safe_load(f)
        else:
            # Create default config based on model info
            config = self._create_default_config(model_info)

        return config

    def _create_default_config(self, model_info: ModelInfo) -> Dict:
        """Create default configuration for models without config files."""
        return {
            "data": {
                "data_dir": "/home/<USER>/ma_yugi/data/07_gnn_ready/",
                "batch_size": 32,
                "num_workers": 4,
                "temporal_windows": [model_info.temporal_window],
                "binary_mapping": {
                    "occupied": [1, 2, 3, 4],
                    "unoccupied": [0]
                },
                "augmentation": {
                    "rotation_angle": 15,
                    "scaling_range": [0.9, 1.1]
                }
            },
            "model": {
                "name": "OccupancyGNN",
                "input_dim": 10 if model_info.temporal_window > 1 else 9,
                "hidden_dim": 128 if "Complex" in model_info.name else 64,
                "output_dim": 1,
                "num_layers": 4 if "Complex" in model_info.name else 3,
                "dropout": 0.3 if "Complex" in model_info.name else 0.2,
                "gnn_type": model_info.architecture.lower(),
                "skip_connections": True,
                "batch_norm": True,
                "pooling": "mean_max",
                "attention_heads": 8 if "Complex" in model_info.name else 4,
                "layer_norm": "Complex" in model_info.name
            }
        }

    def _fix_config_compatibility(self, config: Dict, model_info: ModelInfo) -> Dict:
        """Fix configuration compatibility issues."""
        # Ensure augmentation is present
        if "augmentation" not in config.get("data", {}):
            config["data"]["augmentation"] = {
                "rotation_angle": 15,
                "scaling_range": [0.9, 1.1]
            }

        # Fix input dimension based on actual model requirements
        # Load checkpoint to check actual dimensions
        try:
            checkpoint = torch.load(model_info.checkpoint_path, map_location='cpu')
            state_dict = checkpoint["model_state_dict"]

            # Check embedding layer input dimension
            if "embedding.weight" in state_dict:
                actual_input_dim = state_dict["embedding.weight"].shape[1]
                config["model"]["input_dim"] = actual_input_dim
                print(f"   Fixed input_dim to {actual_input_dim} for {model_info.name}")

            # Check attention heads for GATv2 models
            if model_info.architecture == "GATv2" and "convs.0.att" in state_dict:
                att_shape = state_dict["convs.0.att"].shape
                if len(att_shape) >= 2:
                    actual_heads = att_shape[1]
                    config["model"]["attention_heads"] = actual_heads
                    print(f"   Fixed attention_heads to {actual_heads} for {model_info.name}")

        except Exception as e:
            print(f"   Warning: Could not auto-fix config for {model_info.name}: {e}")

        return config

    def _create_ecc_config(self, checkpoint_dir: Path, temporal_window: int) -> Dict:
        """Create configuration for ECC models."""
        return {
            "data": {
                "data_dir": "/home/<USER>/ma_yugi/data/07_gnn_ready/",
                "batch_size": 32,
                "num_workers": 4,
                "temporal_windows": [temporal_window],
                "binary_mapping": {
                    "occupied": [1, 2, 3, 4],
                    "unoccupied": [0]
                },
                "augmentation": {
                    "rotation_angle": 15,
                    "scaling_range": [0.9, 1.1]
                }
            },
            "model": {
                "name": "OccupancyGNN",
                "input_dim": 10 if temporal_window > 1 else 9,
                "hidden_dim": 64,
                "output_dim": 1,
                "num_layers": 3,
                "dropout": 0.2,
                "gnn_type": "ecc",
                "skip_connections": True,
                "batch_norm": True,
                "pooling": "mean_max"
            }
        }

    def evaluate_model(self, model_info: ModelInfo, max_samples: int = 1000) -> SpatialMetrics:
        """
        Evaluate a single model using polygon-based distance metrics.

        Args:
            model_info: Model information
            max_samples: Maximum number of test samples to evaluate

        Returns:
            Spatial metrics for the model
        """
        print(f"\n🔍 Evaluating model: {model_info.name}")

        # Load model configuration and create data loaders
        config = self.load_model_config(model_info)
        config = self._fix_config_compatibility(config, model_info)
        data_loaders = create_data_loaders(config)
        test_loader = data_loaders[f"temporal_{model_info.temporal_window}"]["test"]

        # Load checkpoint first to get model state
        checkpoint = torch.load(model_info.checkpoint_path, map_location=self.device)

        # Try to create model with config, but handle mismatches
        try:
            model = create_model(config)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"  ✅ Successfully loaded model with original config")
        except RuntimeError as e:
            if "size mismatch" in str(e):
                print(f"  ⚠️ Model architecture mismatch detected")
                print(f"  🔧 Attempting to infer correct architecture from checkpoint...")

                # Infer input dimension from checkpoint
                if 'embedding.weight' in checkpoint['model_state_dict']:
                    embedding_shape = checkpoint['model_state_dict']['embedding.weight'].shape
                    if len(embedding_shape) >= 2:
                        inferred_input_dim = embedding_shape[1]
                        print(f"  📊 Inferred input dimension: {inferred_input_dim}")
                        config['model']['input_dim'] = inferred_input_dim

                # Infer attention heads for GATv2 models
                if 'convs.0.att' in checkpoint['model_state_dict']:
                    att_shape = checkpoint['model_state_dict']['convs.0.att'].shape
                    if len(att_shape) >= 2:
                        inferred_heads = att_shape[1]
                        print(f"  📊 Inferred attention heads: {inferred_heads}")
                        config['model']['attention_heads'] = inferred_heads

                # Try creating model again with inferred parameters
                try:
                    model = create_model(config)
                    model.load_state_dict(checkpoint['model_state_dict'])
                    print(f"  ✅ Successfully loaded model with corrected architecture")
                except RuntimeError:
                    # If we still can't load, try strict=False as last resort
                    print(f"  ⚠️ Still cannot load, trying strict=False")
                    model = create_model(config)
                    model.load_state_dict(checkpoint['model_state_dict'], strict=False)
            else:
                raise e

        model = model.to(self.device)
        model.eval()

        # Collect predictions and ground truth
        all_predictions = []
        all_probabilities = []
        all_ground_truth = []
        all_coordinates = []
        all_distances = []

        samples_processed = 0

        print(f"Processing test samples...")
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc="Evaluating")):
                if samples_processed >= max_samples:
                    break

                batch = batch.to(self.device)

                # Forward pass
                logits = model(batch.x, batch.edge_index, batch.batch)
                probabilities = torch.sigmoid(logits)
                predictions = (probabilities > 0.5).float()

                # Process each graph in the batch
                for graph_idx in range(batch.num_graphs):
                    if samples_processed >= max_samples:
                        break

                    # Extract data for this graph
                    mask = batch.batch == graph_idx
                    graph_predictions = predictions[mask].cpu().numpy().flatten()
                    graph_probabilities = probabilities[mask].cpu().numpy().flatten()
                    graph_ground_truth = batch.y[mask].cpu().numpy().flatten()
                    graph_features = batch.x[mask]

                    # Convert to spatial coordinates
                    coordinates = self.coordinate_mapper.node_features_to_coordinates(graph_features)

                    # Generate ground truth polygons for this sample
                    # For simplification, we'll use fixed workstation polygons
                    # In practice, we'd extract dynamic robot positions from the data
                    workstation_polygons = self.polygon_generator.create_workstation_polygons()
                    boundary_polygons = self.polygon_generator.create_boundary_polygons()
                    all_polygons = workstation_polygons + boundary_polygons

                    # Calculate distances for occupied predictions
                    occupied_mask = graph_predictions == 1
                    if np.any(occupied_mask):
                        occupied_coords = coordinates[occupied_mask, :2]  # Only x, y
                        distances = self.distance_calculator.point_to_polygon_distance(
                            occupied_coords, all_polygons
                        )
                        all_distances.extend(distances)

                    # Store results
                    all_predictions.extend(graph_predictions)
                    all_probabilities.extend(graph_probabilities)
                    all_ground_truth.extend(graph_ground_truth)
                    all_coordinates.extend(coordinates)

                    samples_processed += 1

        # Convert to numpy arrays
        all_predictions = np.array(all_predictions)
        all_probabilities = np.array(all_probabilities)
        all_ground_truth = np.array(all_ground_truth)
        all_distances = np.array(all_distances)

        # Calculate traditional metrics
        accuracy = accuracy_score(all_ground_truth, all_predictions)
        precision = precision_score(all_ground_truth, all_predictions, zero_division=0)
        recall = recall_score(all_ground_truth, all_predictions, zero_division=0)
        f1 = f1_score(all_ground_truth, all_predictions, zero_division=0)

        try:
            roc_auc = roc_auc_score(all_ground_truth, all_probabilities)
        except ValueError:
            roc_auc = 0.0

        # Calculate distance-based metrics
        distance_stats = self.distance_calculator.calculate_distance_statistics(all_distances)

        # Calculate tolerance-based accuracy
        within_15cm = self.distance_calculator.calculate_tolerance_accuracy(all_distances, 0.15)
        within_20cm = self.distance_calculator.calculate_tolerance_accuracy(all_distances, 0.20)
        within_25cm = self.distance_calculator.calculate_tolerance_accuracy(all_distances, 0.25)

        # Create spatial metrics object
        spatial_metrics = SpatialMetrics(
            # Traditional metrics
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1,
            roc_auc=roc_auc,

            # Distance-based metrics
            mean_distance_error=distance_stats['mean'],
            median_distance_error=distance_stats['median'],
            std_distance_error=distance_stats['std'],
            percentile_90_distance=distance_stats['percentile_90'],
            percentile_95_distance=distance_stats['percentile_95'],
            percentile_99_distance=distance_stats['percentile_99'],

            # Tolerance-based accuracy
            within_15cm_accuracy=within_15cm,
            within_20cm_accuracy=within_20cm,
            within_25cm_accuracy=within_25cm,

            # Object-specific metrics (simplified for now)
            workstation_distance_error=distance_stats['mean'],
            robot_distance_error=distance_stats['mean'],
            boundary_distance_error=distance_stats['mean'],

            # Spatial precision metrics
            spatial_precision=precision,  # Simplified
            spatial_recall=recall,  # Simplified

            # Sample counts
            total_predictions=len(all_predictions),
            occupied_predictions=int(np.sum(all_predictions)),
            unoccupied_predictions=int(len(all_predictions) - np.sum(all_predictions))
        )

        print(f"✅ Completed evaluation for {model_info.name}")
        print(f"   Traditional Accuracy: {accuracy:.4f}")
        print(f"   Mean Distance Error: {distance_stats['mean']:.4f}m")
        print(f"   Within 20cm Accuracy: {within_20cm:.4f}")

        return spatial_metrics

    def evaluate_all_models(self, max_samples: int = 1000) -> Dict[str, SpatialMetrics]:
        """
        Evaluate all discovered models.

        Args:
            max_samples: Maximum number of test samples per model

        Returns:
            Dictionary of model results
        """
        print(f"🚀 Starting comprehensive polygon-based distance evaluation...")
        print(f"📊 Found {len(self.models)} models to evaluate")

        results = {}

        for model_info in self.models:
            try:
                metrics = self.evaluate_model(model_info, max_samples)
                results[model_info.name] = metrics

                # Save intermediate results
                self._save_model_results(model_info.name, metrics)

            except Exception as e:
                print(f"❌ Failed to evaluate {model_info.name}: {e}")
                import traceback
                traceback.print_exc()
                continue

        print(f"🎯 Completed evaluation of {len(results)} models")
        return results

    def _save_model_results(self, model_name: str, metrics: SpatialMetrics):
        """Save individual model results."""
        results_dict = {
            'model_name': model_name,
            'timestamp': datetime.now().isoformat(),
            'traditional_metrics': {
                'accuracy': metrics.accuracy,
                'precision': metrics.precision,
                'recall': metrics.recall,
                'f1_score': metrics.f1_score,
                'roc_auc': metrics.roc_auc
            },
            'distance_metrics': {
                'mean_distance_error': metrics.mean_distance_error,
                'median_distance_error': metrics.median_distance_error,
                'std_distance_error': metrics.std_distance_error,
                'percentile_90_distance': metrics.percentile_90_distance,
                'percentile_95_distance': metrics.percentile_95_distance,
                'percentile_99_distance': metrics.percentile_99_distance
            },
            'tolerance_metrics': {
                'within_15cm_accuracy': metrics.within_15cm_accuracy,
                'within_20cm_accuracy': metrics.within_20cm_accuracy,
                'within_25cm_accuracy': metrics.within_25cm_accuracy
            },
            'sample_counts': {
                'total_predictions': metrics.total_predictions,
                'occupied_predictions': metrics.occupied_predictions,
                'unoccupied_predictions': metrics.unoccupied_predictions
            }
        }

        # Save to JSON
        output_file = self.output_dir / f"{model_name}_polygon_distance_results.json"
        with open(output_file, 'w') as f:
            json.dump(results_dict, f, indent=2)

    def create_comparison_visualizations(self, results: Dict[str, SpatialMetrics]):
        """Create comprehensive comparison visualizations."""
        print("📊 Creating comparison visualizations...")

        # Prepare data for visualization
        model_names = list(results.keys())

        # Traditional metrics
        traditional_metrics = {
            'Accuracy': [results[name].accuracy for name in model_names],
            'Precision': [results[name].precision for name in model_names],
            'Recall': [results[name].recall for name in model_names],
            'F1-Score': [results[name].f1_score for name in model_names],
            'ROC-AUC': [results[name].roc_auc for name in model_names]
        }

        # Distance metrics
        distance_metrics = {
            'Mean Distance Error (m)': [results[name].mean_distance_error for name in model_names],
            'Median Distance Error (m)': [results[name].median_distance_error for name in model_names],
            '90th Percentile Distance (m)': [results[name].percentile_90_distance for name in model_names],
            '95th Percentile Distance (m)': [results[name].percentile_95_distance for name in model_names]
        }

        # Tolerance metrics
        tolerance_metrics = {
            'Within 15cm': [results[name].within_15cm_accuracy for name in model_names],
            'Within 20cm': [results[name].within_20cm_accuracy for name in model_names],
            'Within 25cm': [results[name].within_25cm_accuracy for name in model_names]
        }

        # Create comprehensive comparison plot
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('Comprehensive Model Comparison: Polygon-Based Distance Evaluation', fontsize=16, fontweight='bold')

        # Traditional metrics comparison
        ax = axes[0, 0]
        df_traditional = pd.DataFrame(traditional_metrics, index=model_names)
        df_traditional.plot(kind='bar', ax=ax, width=0.8)
        ax.set_title('Traditional Classification Metrics', fontweight='bold')
        ax.set_ylabel('Score')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.tick_params(axis='x', rotation=45)

        # Distance metrics comparison
        ax = axes[0, 1]
        df_distance = pd.DataFrame(distance_metrics, index=model_names)
        df_distance.plot(kind='bar', ax=ax, width=0.8)
        ax.set_title('Distance-Based Error Metrics', fontweight='bold')
        ax.set_ylabel('Distance (meters)')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.tick_params(axis='x', rotation=45)

        # Tolerance metrics comparison
        ax = axes[0, 2]
        df_tolerance = pd.DataFrame(tolerance_metrics, index=model_names)
        df_tolerance.plot(kind='bar', ax=ax, width=0.8)
        ax.set_title('Tolerance-Based Accuracy', fontweight='bold')
        ax.set_ylabel('Accuracy')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.tick_params(axis='x', rotation=45)

        # Distance error distribution
        ax = axes[1, 0]
        mean_errors = [results[name].mean_distance_error for name in model_names]
        std_errors = [results[name].std_distance_error for name in model_names]

        bars = ax.bar(range(len(model_names)), mean_errors, yerr=std_errors, capsize=5)
        ax.set_title('Mean Distance Error with Standard Deviation', fontweight='bold')
        ax.set_ylabel('Distance Error (meters)')
        ax.set_xticks(range(len(model_names)))
        ax.set_xticklabels(model_names, rotation=45, ha='right')

        # Add value labels on bars
        for i, (bar, mean_err) in enumerate(zip(bars, mean_errors)):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std_errors[i] + 0.001,
                   f'{mean_err:.3f}m', ha='center', va='bottom', fontsize=8)

        # Architecture comparison
        ax = axes[1, 1]
        architectures = {}
        for name in model_names:
            arch = name.split('_')[0]  # Extract architecture name
            if arch not in architectures:
                architectures[arch] = []
            architectures[arch].append(results[name].mean_distance_error)

        arch_names = list(architectures.keys())
        arch_means = [np.mean(architectures[arch]) for arch in arch_names]
        arch_stds = [np.std(architectures[arch]) if len(architectures[arch]) > 1 else 0 for arch in arch_names]

        bars = ax.bar(arch_names, arch_means, yerr=arch_stds, capsize=5)
        ax.set_title('Architecture Comparison (Mean Distance Error)', fontweight='bold')
        ax.set_ylabel('Mean Distance Error (meters)')
        ax.tick_params(axis='x', rotation=45)

        # Temporal window comparison
        ax = axes[1, 2]
        temporal_windows = {}
        for name in model_names:
            if 'T3' in name:
                tw = 'Temporal Window 3'
            elif 'T5' in name:
                tw = 'Temporal Window 5'
            else:
                tw = 'Unknown'

            if tw not in temporal_windows:
                temporal_windows[tw] = []
            temporal_windows[tw].append(results[name].mean_distance_error)

        tw_names = list(temporal_windows.keys())
        tw_means = [np.mean(temporal_windows[tw]) for tw in tw_names]
        tw_stds = [np.std(temporal_windows[tw]) if len(temporal_windows[tw]) > 1 else 0 for tw in tw_names]

        bars = ax.bar(tw_names, tw_means, yerr=tw_stds, capsize=5)
        ax.set_title('Temporal Window Comparison (Mean Distance Error)', fontweight='bold')
        ax.set_ylabel('Mean Distance Error (meters)')

        plt.tight_layout()

        # Save the plot
        output_file = self.output_dir / "comprehensive_model_comparison.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.savefig(output_file.with_suffix('.pdf'), bbox_inches='tight')
        print(f"💾 Saved comparison plot: {output_file}")

        plt.show()

        return fig

    def generate_summary_report(self, results: Dict[str, SpatialMetrics]):
        """Generate a comprehensive summary report."""
        print("📝 Generating summary report...")

        # Create summary DataFrame
        summary_data = []
        for model_name, metrics in results.items():
            summary_data.append({
                'Model': model_name,
                'Architecture': model_name.split('_')[0],
                'Temporal_Window': 'T3' if 'T3' in model_name else 'T5',
                'Accuracy': metrics.accuracy,
                'Precision': metrics.precision,
                'Recall': metrics.recall,
                'F1_Score': metrics.f1_score,
                'ROC_AUC': metrics.roc_auc,
                'Mean_Distance_Error': metrics.mean_distance_error,
                'Median_Distance_Error': metrics.median_distance_error,
                'Std_Distance_Error': metrics.std_distance_error,
                'P90_Distance': metrics.percentile_90_distance,
                'P95_Distance': metrics.percentile_95_distance,
                'P99_Distance': metrics.percentile_99_distance,
                'Within_15cm': metrics.within_15cm_accuracy,
                'Within_20cm': metrics.within_20cm_accuracy,
                'Within_25cm': metrics.within_25cm_accuracy,
                'Total_Predictions': metrics.total_predictions,
                'Occupied_Predictions': metrics.occupied_predictions
            })

        df_summary = pd.DataFrame(summary_data)

        # Save detailed CSV
        csv_file = self.output_dir / "polygon_distance_evaluation_results.csv"
        df_summary.to_csv(csv_file, index=False)
        print(f"💾 Saved detailed results: {csv_file}")

        # Generate markdown report
        report_file = self.output_dir / "POLYGON_DISTANCE_EVALUATION_REPORT.md"

        with open(report_file, 'w') as f:
            f.write("# Polygon-Based Distance Evaluation Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**Models Evaluated:** {len(results)}\n\n")

            f.write("## Executive Summary\n\n")

            # Find best performing models
            best_accuracy = df_summary.loc[df_summary['Accuracy'].idxmax()]
            best_distance = df_summary.loc[df_summary['Mean_Distance_Error'].idxmin()]
            best_tolerance = df_summary.loc[df_summary['Within_20cm'].idxmax()]

            f.write(f"- **Best Traditional Accuracy:** {best_accuracy['Model']} ({best_accuracy['Accuracy']:.4f})\n")
            f.write(f"- **Best Distance Accuracy:** {best_distance['Model']} ({best_distance['Mean_Distance_Error']:.4f}m)\n")
            f.write(f"- **Best 20cm Tolerance:** {best_tolerance['Model']} ({best_tolerance['Within_20cm']:.4f})\n\n")

            f.write("## Detailed Results\n\n")
            f.write("### Traditional Classification Metrics\n\n")
            f.write(df_summary[['Model', 'Accuracy', 'Precision', 'Recall', 'F1_Score', 'ROC_AUC']].to_markdown(index=False))
            f.write("\n\n")

            f.write("### Distance-Based Metrics\n\n")
            f.write(df_summary[['Model', 'Mean_Distance_Error', 'Median_Distance_Error', 'P90_Distance', 'P95_Distance']].to_markdown(index=False))
            f.write("\n\n")

            f.write("### Tolerance-Based Accuracy\n\n")
            f.write(df_summary[['Model', 'Within_15cm', 'Within_20cm', 'Within_25cm']].to_markdown(index=False))
            f.write("\n\n")

            f.write("## Architecture Analysis\n\n")
            arch_analysis = df_summary.groupby('Architecture').agg({
                'Mean_Distance_Error': ['mean', 'std'],
                'Accuracy': ['mean', 'std'],
                'Within_20cm': ['mean', 'std']
            }).round(4)
            f.write(arch_analysis.to_markdown())
            f.write("\n\n")

            f.write("## Temporal Window Analysis\n\n")
            temp_analysis = df_summary.groupby('Temporal_Window').agg({
                'Mean_Distance_Error': ['mean', 'std'],
                'Accuracy': ['mean', 'std'],
                'Within_20cm': ['mean', 'std']
            }).round(4)
            f.write(temp_analysis.to_markdown())
            f.write("\n\n")

            f.write("## Key Insights\n\n")
            f.write("1. **Spatial Accuracy vs Traditional Metrics:** Distance-based evaluation provides more nuanced insights into model performance for spatial applications.\n\n")
            f.write("2. **Architecture Performance:** Different GNN architectures show varying spatial accuracy characteristics.\n\n")
            f.write("3. **Temporal Window Impact:** The choice of temporal window affects both traditional and spatial accuracy metrics.\n\n")
            f.write("4. **Tolerance Analysis:** Models show different performance profiles when evaluated at various distance tolerances.\n\n")

        print(f"📄 Generated report: {report_file}")

        return df_summary


def main():
    """Main execution function."""
    print("🚀 Starting Comprehensive Polygon-Based Distance Evaluation System")
    print("=" * 80)

    # Initialize evaluator
    evaluator = ModelEvaluator()

    if not evaluator.models:
        print("❌ No models found for evaluation!")
        return

    # Run evaluation on all models
    results = evaluator.evaluate_all_models(max_samples=1000)

    if not results:
        print("❌ No successful evaluations!")
        return

    # Create visualizations
    evaluator.create_comparison_visualizations(results)

    # Generate summary report
    summary_df = evaluator.generate_summary_report(results)

    print("\n" + "=" * 80)
    print("🎯 EVALUATION COMPLETED SUCCESSFULLY!")
    print("=" * 80)
    print(f"📊 Models evaluated: {len(results)}")
    print(f"📁 Results saved to: {evaluator.output_dir}")
    print("\n📈 Top 3 Models by Distance Accuracy:")

    # Show top performers
    top_models = summary_df.nsmallest(3, 'Mean_Distance_Error')
    for idx, row in top_models.iterrows():
        print(f"   {idx+1}. {row['Model']}: {row['Mean_Distance_Error']:.4f}m mean error")

    print("\n✅ Check the output directory for detailed results and visualizations!")


if __name__ == "__main__":
    main()
