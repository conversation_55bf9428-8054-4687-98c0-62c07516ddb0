#!/usr/bin/env python3
"""
Simplified Polygon-Based Distance Evaluation System

This system builds on the existing comprehensive evaluation framework
to add polygon-based distance metrics for spatial accuracy assessment.

Author: AI Assistant
Date: 2024
"""

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from tqdm import tqdm
import yaml
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import the working comprehensive evaluation system
from comprehensive_2d_iou_evaluation import Comprehensive2DIoUEvaluator, ModelInfo, IoUResults
from data import OccupancyDataset
from torch_geometric.loader import DataLoader

# Geometric computation libraries
from shapely.geometry import Point, Polygon, LineString
from shapely.ops import unary_union
from scipy.spatial.distance import cdist
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score


@dataclass
class PolygonDistanceResults:
    """Results from polygon-based distance evaluation."""
    model_name: str
    
    # Traditional metrics
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    roc_auc: float
    
    # Distance-based metrics
    mean_distance_error: float
    median_distance_error: float
    std_distance_error: float
    percentile_90_distance: float
    percentile_95_distance: float
    percentile_99_distance: float
    
    # Tolerance-based accuracy
    within_15cm_accuracy: float
    within_20cm_accuracy: float
    within_25cm_accuracy: float
    
    # Sample information
    total_samples: int
    total_predictions: int
    occupied_predictions: int
    
    # Grid information
    grid_resolution: float


class PolygonDistanceEvaluator(Comprehensive2DIoUEvaluator):
    """
    Extended evaluator that adds polygon-based distance metrics
    to the existing comprehensive IoU evaluation system.
    """
    
    def __init__(self, models_dir: str = "models_final", results_dir: str = "polygon_distance_evaluation"):
        """Initialize the polygon distance evaluator."""
        super().__init__(models_dir, results_dir)
        
        # Arena specifications for polygon generation
        self.arena_bounds = (-10.39, 10.67, -5.31, 6.50)  # (min_x, max_x, min_y, max_y)
        self.voxel_size = 0.1  # meters
        
        # Object dimensions
        self.workstation_width = 1.0  # meters
        self.workstation_height = 0.65  # meters
        self.robot_width = 0.32  # meters
        self.robot_height = 0.24  # meters
        
        # Fixed workstation positions (simplified)
        self.workstation_positions = [
            (0.0, 0.0, 0.0),    # (x, y, rotation_deg)
            (2.0, 0.0, 0.0),
            (4.0, 0.0, 0.0),
            (6.0, 0.0, 90.0),   # Vertical
            (8.0, 0.0, 0.0),
        ]
        
        # Storage for polygon distance results
        self.polygon_results: Dict[str, PolygonDistanceResults] = {}
    
    def create_workstation_polygons(self) -> List[Polygon]:
        """Create polygons for all workstations."""
        polygons = []
        
        for x, y, rotation_deg in self.workstation_positions:
            polygon = self._create_rotated_rectangle(
                x, y, self.workstation_width, self.workstation_height, rotation_deg
            )
            polygons.append(polygon)
            
        return polygons
    
    def _create_rotated_rectangle(self, center_x: float, center_y: float, 
                                width: float, height: float, rotation_deg: float) -> Polygon:
        """Create a rotated rectangle polygon."""
        # Create rectangle corners relative to center
        half_w, half_h = width / 2, height / 2
        corners = np.array([
            [-half_w, -half_h],
            [half_w, -half_h],
            [half_w, half_h],
            [-half_w, half_h]
        ])
        
        # Apply rotation
        rotation_rad = np.radians(rotation_deg)
        cos_r, sin_r = np.cos(rotation_rad), np.sin(rotation_rad)
        rotation_matrix = np.array([[cos_r, -sin_r], [sin_r, cos_r]])
        
        rotated_corners = corners @ rotation_matrix.T
        
        # Translate to center position
        final_corners = rotated_corners + np.array([center_x, center_y])
        
        return Polygon(final_corners)
    
    def point_to_polygon_distance(self, points: np.ndarray, polygons: List[Polygon]) -> np.ndarray:
        """Calculate minimum distance from points to polygon boundaries."""
        if not polygons or len(points) == 0:
            return np.array([])
            
        min_distances = np.full(len(points), np.inf)
        
        for polygon in polygons:
            boundary = polygon.boundary
            
            for i, point in enumerate(points):
                point_geom = Point(point)
                distance = point_geom.distance(boundary)
                min_distances[i] = min(min_distances[i], distance)
                
        return min_distances
    
    def calculate_tolerance_accuracy(self, distances: np.ndarray, tolerance: float) -> float:
        """Calculate accuracy within a distance tolerance."""
        if len(distances) == 0:
            return 0.0
        return np.mean(distances <= tolerance)

    def _load_model_with_inference(self, config, checkpoint):
        """Load model with parameter inference from checkpoint."""
        from model import create_model

        try:
            model = create_model(config)
            model.load_state_dict(checkpoint['model_state_dict'])
            return model
        except RuntimeError as e:
            if "size mismatch" in str(e):
                print(f"  ⚠️ Model architecture mismatch detected")
                print(f"  🔧 Attempting to infer correct architecture from checkpoint...")

                # Infer input dimension from checkpoint
                if 'embedding.weight' in checkpoint['model_state_dict']:
                    embedding_shape = checkpoint['model_state_dict']['embedding.weight'].shape
                    if len(embedding_shape) >= 2:
                        inferred_input_dim = embedding_shape[1]
                        print(f"  📊 Inferred input dimension: {inferred_input_dim}")
                        config['model']['input_dim'] = inferred_input_dim

                # Infer attention heads for GATv2 models
                if 'convs.0.att' in checkpoint['model_state_dict']:
                    att_shape = checkpoint['model_state_dict']['convs.0.att'].shape
                    if len(att_shape) >= 2:
                        inferred_heads = att_shape[1]
                        print(f"  📊 Inferred attention heads: {inferred_heads}")
                        config['model']['attention_heads'] = inferred_heads

                # Infer hidden dimension for ECC models
                if 'embedding.weight' in checkpoint['model_state_dict']:
                    embedding_shape = checkpoint['model_state_dict']['embedding.weight'].shape
                    if len(embedding_shape) >= 2:
                        inferred_hidden_dim = embedding_shape[0]
                        print(f"  📊 Inferred hidden dimension: {inferred_hidden_dim}")
                        config['model']['hidden_dim'] = inferred_hidden_dim

                # Try creating model again with inferred parameters
                try:
                    model = create_model(config)
                    model.load_state_dict(checkpoint['model_state_dict'])
                    print(f"  ✅ Successfully loaded model with corrected architecture")
                    return model
                except RuntimeError:
                    # If we still can't load, try strict=False as last resort
                    print(f"  ⚠️ Still cannot load, trying strict=False")
                    model = create_model(config)
                    model.load_state_dict(checkpoint['model_state_dict'], strict=False)
                    return model
            else:
                raise e

    def evaluate_model_polygon_distance(self, model_info: ModelInfo, max_samples: int = 500) -> PolygonDistanceResults:
        """
        Evaluate a single model using polygon-based distance metrics.

        This method extends the existing IoU evaluation with distance-based metrics.
        """
        print(f"🔍 Evaluating polygon distance for model: {model_info.name}")

        # Load model and data (reuse from parent class)
        if 'ecc' in model_info.name.lower():
            config = self._create_ecc_config(Path(model_info.checkpoint_path).parent, model_info.temporal_window)
        else:
            with open(model_info.config_path, 'r') as f:
                config = yaml.safe_load(f)

        # Load model with the robust loading from parent class
        checkpoint = torch.load(model_info.checkpoint_path, map_location=self.device)

        try:
            model = self._load_model_with_inference(config, checkpoint)
        except Exception as e:
            print(f"❌ Failed to load model for polygon evaluation: {e}")
            return PolygonDistanceResults(
                model_name=model_info.name,
                accuracy=0.0, precision=0.0, recall=0.0, f1_score=0.0, roc_auc=0.0,
                mean_distance_error=0.0, median_distance_error=0.0, std_distance_error=0.0,
                percentile_90_distance=0.0, percentile_95_distance=0.0, percentile_99_distance=0.0,
                within_15cm_accuracy=0.0, within_20cm_accuracy=0.0, within_25cm_accuracy=0.0,
                total_samples=0, total_predictions=0, occupied_predictions=0,
                grid_resolution=self.grid_config.resolution
            )

        model.to(self.device)
        model.eval()

        # Load test data
        test_loader = self.load_test_data(model_info.temporal_window)

        # Check if test loader has data
        print(f"  📊 Test loader has {len(test_loader)} batches")
        if len(test_loader) == 0:
            print(f"  ⚠️ No test data found for temporal window {model_info.temporal_window}")
            return PolygonDistanceResults(
                model_name=model_info.name,
                accuracy=0.0, precision=0.0, recall=0.0, f1_score=0.0, roc_auc=0.0,
                mean_distance_error=0.0, median_distance_error=0.0, std_distance_error=0.0,
                percentile_90_distance=0.0, percentile_95_distance=0.0, percentile_99_distance=0.0,
                within_15cm_accuracy=0.0, within_20cm_accuracy=0.0, within_25cm_accuracy=0.0,
                total_samples=0, total_predictions=0, occupied_predictions=0,
                grid_resolution=self.grid_config.resolution
            )

        # Create ground truth polygons
        workstation_polygons = self.create_workstation_polygons()

        # Collect predictions and calculate distances
        all_distances = []
        all_predictions = []
        all_ground_truth = []
        all_probabilities = []

        samples_processed = 0

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc="Processing samples")):
                if samples_processed >= max_samples:
                    break

                try:
                    batch = batch.to(self.device)

                    # Forward pass
                    logits = model(batch.x, batch.edge_index, batch.batch)
                    probabilities = torch.sigmoid(logits).cpu().numpy()
                    predictions = (probabilities > 0.5).astype(int)
                    ground_truth = batch.y.cpu().numpy()

                    # Handle different prediction formats
                    if len(predictions) == 1 and len(ground_truth) > 1:
                        # Graph-level prediction: broadcast to all nodes
                        predictions = np.full(len(ground_truth), predictions[0])
                    elif len(predictions) != len(ground_truth):
                        print(f"    ⚠️ Prediction-label size mismatch: {len(predictions)} vs {len(ground_truth)}")
                        continue

                    # Extract spatial coordinates from node features
                    # Try different coordinate sources
                    try:
                        # First try pos attribute
                        coordinates = batch.pos[:, :2].cpu().numpy()  # x, y coordinates
                    except (AttributeError, IndexError):
                        try:
                            # Fallback to features 3, 4 (x, y coordinates based on our analysis)
                            coordinates = batch.x[:, 3:5].cpu().numpy()
                        except IndexError:
                            print(f"    ⚠️ Cannot extract coordinates from batch {batch_idx}")
                            continue

                    # Find occupied predictions
                    occupied_mask = predictions.flatten() == 1
                    if np.any(occupied_mask):
                        occupied_coords = coordinates[occupied_mask]

                        # Calculate distances to workstation polygons
                        distances = self.point_to_polygon_distance(occupied_coords, workstation_polygons)
                        all_distances.extend(distances)

                    # Store all predictions for traditional metrics
                    all_predictions.extend(predictions.flatten())
                    all_ground_truth.extend(ground_truth.flatten())
                    all_probabilities.extend(probabilities.flatten())

                    samples_processed += 1

                except Exception as e:
                    print(f"    ⚠️ Error processing batch {batch_idx}: {e}")
                    continue

        # Convert to numpy arrays
        all_predictions = np.array(all_predictions)
        all_ground_truth = np.array(all_ground_truth)
        all_probabilities = np.array(all_probabilities)
        all_distances = np.array(all_distances)

        # Calculate traditional metrics
        accuracy = accuracy_score(all_ground_truth, all_predictions)
        precision = precision_score(all_ground_truth, all_predictions, zero_division=0)
        recall = recall_score(all_ground_truth, all_predictions, zero_division=0)
        f1 = f1_score(all_ground_truth, all_predictions, zero_division=0)

        try:
            roc_auc = roc_auc_score(all_ground_truth, all_probabilities)
        except ValueError:
            roc_auc = 0.0

        # Calculate distance-based metrics
        if len(all_distances) > 0:
            mean_distance = float(np.mean(all_distances))
            median_distance = float(np.median(all_distances))
            std_distance = float(np.std(all_distances))
            p90_distance = float(np.percentile(all_distances, 90))
            p95_distance = float(np.percentile(all_distances, 95))
            p99_distance = float(np.percentile(all_distances, 99))

            # Tolerance-based accuracy
            within_15cm = self.calculate_tolerance_accuracy(all_distances, 0.15)
            within_20cm = self.calculate_tolerance_accuracy(all_distances, 0.20)
            within_25cm = self.calculate_tolerance_accuracy(all_distances, 0.25)
        else:
            mean_distance = median_distance = std_distance = 0.0
            p90_distance = p95_distance = p99_distance = 0.0
            within_15cm = within_20cm = within_25cm = 0.0

        # Create results object
        results = PolygonDistanceResults(
            model_name=model_info.name,
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1,
            roc_auc=roc_auc,
            mean_distance_error=mean_distance,
            median_distance_error=median_distance,
            std_distance_error=std_distance,
            percentile_90_distance=p90_distance,
            percentile_95_distance=p95_distance,
            percentile_99_distance=p99_distance,
            within_15cm_accuracy=within_15cm,
            within_20cm_accuracy=within_20cm,
            within_25cm_accuracy=within_25cm,
            total_samples=samples_processed,
            total_predictions=len(all_predictions),
            occupied_predictions=int(np.sum(all_predictions)),
            grid_resolution=self.grid_config.resolution
        )

        print(f"  ✅ Completed polygon distance evaluation")
        print(f"     Traditional Accuracy: {accuracy:.4f}")
        print(f"     Mean Distance Error: {mean_distance:.4f}m")
        print(f"     Within 20cm Accuracy: {within_20cm:.4f}")

        return results

    def evaluate_all_models_polygon_distance(self, max_samples: int = 500) -> Dict[str, PolygonDistanceResults]:
        """Evaluate all models using polygon-based distance metrics."""
        print("🚀 Starting comprehensive polygon-based distance evaluation...")

        if not self.models:
            self.discover_models()

        results = {}

        for model_info in self.models:
            try:
                polygon_results = self.evaluate_model_polygon_distance(model_info, max_samples)
                results[model_info.name] = polygon_results
                self.polygon_results[model_info.name] = polygon_results

                # Save intermediate results
                self._save_polygon_results(model_info.name, polygon_results)

            except Exception as e:
                print(f"❌ Failed to evaluate {model_info.name}: {e}")
                import traceback
                traceback.print_exc()
                continue

        print(f"🎯 Completed polygon distance evaluation of {len(results)} models")
        return results

    def _save_polygon_results(self, model_name: str, results: PolygonDistanceResults):
        """Save individual model polygon distance results."""
        results_dict = {
            'model_name': model_name,
            'timestamp': datetime.now().isoformat(),
            'traditional_metrics': {
                'accuracy': results.accuracy,
                'precision': results.precision,
                'recall': results.recall,
                'f1_score': results.f1_score,
                'roc_auc': results.roc_auc
            },
            'distance_metrics': {
                'mean_distance_error': results.mean_distance_error,
                'median_distance_error': results.median_distance_error,
                'std_distance_error': results.std_distance_error,
                'percentile_90_distance': results.percentile_90_distance,
                'percentile_95_distance': results.percentile_95_distance,
                'percentile_99_distance': results.percentile_99_distance
            },
            'tolerance_metrics': {
                'within_15cm_accuracy': results.within_15cm_accuracy,
                'within_20cm_accuracy': results.within_20cm_accuracy,
                'within_25cm_accuracy': results.within_25cm_accuracy
            },
            'sample_info': {
                'total_samples': results.total_samples,
                'total_predictions': results.total_predictions,
                'occupied_predictions': results.occupied_predictions,
                'grid_resolution': results.grid_resolution
            }
        }

        # Save to JSON
        output_file = self.results_dir / f"{model_name}_polygon_distance_results.json"
        with open(output_file, 'w') as f:
            json.dump(results_dict, f, indent=2)

    def create_polygon_distance_visualizations(self, results: Dict[str, PolygonDistanceResults]):
        """Create comprehensive polygon distance comparison visualizations."""
        print("📊 Creating polygon distance comparison visualizations...")

        if not results:
            print("❌ No results to visualize!")
            return

        # Prepare data for visualization
        model_names = list(results.keys())

        # Create comprehensive comparison plot
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('Polygon-Based Distance Evaluation: Comprehensive Model Comparison',
                     fontsize=16, fontweight='bold')

        # Traditional metrics comparison
        ax = axes[0, 0]
        traditional_data = {
            'Accuracy': [results[name].accuracy for name in model_names],
            'Precision': [results[name].precision for name in model_names],
            'Recall': [results[name].recall for name in model_names],
            'F1-Score': [results[name].f1_score for name in model_names]
        }
        df_traditional = pd.DataFrame(traditional_data, index=model_names)
        df_traditional.plot(kind='bar', ax=ax, width=0.8)
        ax.set_title('Traditional Classification Metrics', fontweight='bold')
        ax.set_ylabel('Score')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.tick_params(axis='x', rotation=45)

        # Distance metrics comparison
        ax = axes[0, 1]
        distance_data = {
            'Mean Distance (m)': [results[name].mean_distance_error for name in model_names],
            'Median Distance (m)': [results[name].median_distance_error for name in model_names],
            '90th Percentile (m)': [results[name].percentile_90_distance for name in model_names]
        }
        df_distance = pd.DataFrame(distance_data, index=model_names)
        df_distance.plot(kind='bar', ax=ax, width=0.8)
        ax.set_title('Distance-Based Error Metrics', fontweight='bold')
        ax.set_ylabel('Distance (meters)')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.tick_params(axis='x', rotation=45)

        # Tolerance metrics comparison
        ax = axes[0, 2]
        tolerance_data = {
            'Within 15cm': [results[name].within_15cm_accuracy for name in model_names],
            'Within 20cm': [results[name].within_20cm_accuracy for name in model_names],
            'Within 25cm': [results[name].within_25cm_accuracy for name in model_names]
        }
        df_tolerance = pd.DataFrame(tolerance_data, index=model_names)
        df_tolerance.plot(kind='bar', ax=ax, width=0.8)
        ax.set_title('Tolerance-Based Accuracy', fontweight='bold')
        ax.set_ylabel('Accuracy')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.tick_params(axis='x', rotation=45)

        # Mean distance error with error bars
        ax = axes[1, 0]
        mean_errors = [results[name].mean_distance_error for name in model_names]
        std_errors = [results[name].std_distance_error for name in model_names]

        bars = ax.bar(range(len(model_names)), mean_errors, yerr=std_errors, capsize=5)
        ax.set_title('Mean Distance Error with Standard Deviation', fontweight='bold')
        ax.set_ylabel('Distance Error (meters)')
        ax.set_xticks(range(len(model_names)))
        ax.set_xticklabels(model_names, rotation=45, ha='right')

        # Add value labels on bars
        for i, (bar, mean_err) in enumerate(zip(bars, mean_errors)):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std_errors[i] + 0.001,
                   f'{mean_err:.3f}m', ha='center', va='bottom', fontsize=8)

        # Architecture comparison
        ax = axes[1, 1]
        architectures = {}
        for name in model_names:
            arch = name.split('_')[0]  # Extract architecture name
            if arch not in architectures:
                architectures[arch] = []
            architectures[arch].append(results[name].mean_distance_error)

        arch_names = list(architectures.keys())
        arch_means = [np.mean(architectures[arch]) for arch in arch_names]
        arch_stds = [np.std(architectures[arch]) if len(architectures[arch]) > 1 else 0 for arch in arch_names]

        bars = ax.bar(arch_names, arch_means, yerr=arch_stds, capsize=5)
        ax.set_title('Architecture Comparison (Mean Distance Error)', fontweight='bold')
        ax.set_ylabel('Mean Distance Error (meters)')
        ax.tick_params(axis='x', rotation=45)

        # Temporal window comparison
        ax = axes[1, 2]
        temporal_windows = {}
        for name in model_names:
            if 'temp3' in name.lower() or 't3' in name:
                tw = 'Temporal Window 3'
            elif 'temp5' in name.lower() or 't5' in name:
                tw = 'Temporal Window 5'
            else:
                tw = 'Unknown'

            if tw not in temporal_windows:
                temporal_windows[tw] = []
            temporal_windows[tw].append(results[name].mean_distance_error)

        tw_names = list(temporal_windows.keys())
        tw_means = [np.mean(temporal_windows[tw]) for tw in tw_names]
        tw_stds = [np.std(temporal_windows[tw]) if len(temporal_windows[tw]) > 1 else 0 for tw in tw_names]

        bars = ax.bar(tw_names, tw_means, yerr=tw_stds, capsize=5)
        ax.set_title('Temporal Window Comparison (Mean Distance Error)', fontweight='bold')
        ax.set_ylabel('Mean Distance Error (meters)')

        plt.tight_layout()

        # Save the plot
        output_file = self.results_dir / "polygon_distance_comprehensive_comparison.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.savefig(output_file.with_suffix('.pdf'), bbox_inches='tight')
        print(f"💾 Saved comparison plot: {output_file}")

        plt.show()

        return fig

    def generate_polygon_distance_report(self, results: Dict[str, PolygonDistanceResults]):
        """Generate a comprehensive polygon distance evaluation report."""
        print("📝 Generating polygon distance evaluation report...")

        # Create summary DataFrame
        summary_data = []
        for model_name, metrics in results.items():
            summary_data.append({
                'Model': model_name,
                'Architecture': model_name.split('_')[0],
                'Temporal_Window': 'T3' if 'temp3' in model_name.lower() or 't3' in model_name else 'T5',
                'Accuracy': metrics.accuracy,
                'Precision': metrics.precision,
                'Recall': metrics.recall,
                'F1_Score': metrics.f1_score,
                'ROC_AUC': metrics.roc_auc,
                'Mean_Distance_Error': metrics.mean_distance_error,
                'Median_Distance_Error': metrics.median_distance_error,
                'Std_Distance_Error': metrics.std_distance_error,
                'P90_Distance': metrics.percentile_90_distance,
                'P95_Distance': metrics.percentile_95_distance,
                'P99_Distance': metrics.percentile_99_distance,
                'Within_15cm': metrics.within_15cm_accuracy,
                'Within_20cm': metrics.within_20cm_accuracy,
                'Within_25cm': metrics.within_25cm_accuracy,
                'Total_Samples': metrics.total_samples,
                'Total_Predictions': metrics.total_predictions,
                'Occupied_Predictions': metrics.occupied_predictions
            })

        df_summary = pd.DataFrame(summary_data)

        # Save detailed CSV
        csv_file = self.results_dir / "polygon_distance_evaluation_results.csv"
        df_summary.to_csv(csv_file, index=False)
        print(f"💾 Saved detailed results: {csv_file}")

        # Generate markdown report
        report_file = self.results_dir / "POLYGON_DISTANCE_EVALUATION_REPORT.md"

        with open(report_file, 'w') as f:
            f.write("# Polygon-Based Distance Evaluation Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**Models Evaluated:** {len(results)}\n\n")

            f.write("## Executive Summary\n\n")

            # Find best performing models
            best_accuracy = df_summary.loc[df_summary['Accuracy'].idxmax()]
            best_distance = df_summary.loc[df_summary['Mean_Distance_Error'].idxmin()]
            best_tolerance = df_summary.loc[df_summary['Within_20cm'].idxmax()]

            f.write(f"- **Best Traditional Accuracy:** {best_accuracy['Model']} ({best_accuracy['Accuracy']:.4f})\n")
            f.write(f"- **Best Distance Accuracy:** {best_distance['Model']} ({best_distance['Mean_Distance_Error']:.4f}m)\n")
            f.write(f"- **Best 20cm Tolerance:** {best_tolerance['Model']} ({best_tolerance['Within_20cm']:.4f})\n\n")

            f.write("## Key Findings\n\n")
            f.write("### Spatial Accuracy Insights\n\n")
            f.write("1. **Distance-based evaluation provides more nuanced insights** into model performance for spatial applications compared to traditional IoU metrics.\n\n")
            f.write("2. **Tolerance-based accuracy** shows how models perform at different precision requirements for collaborative robotics.\n\n")
            f.write("3. **Architecture differences** are more pronounced when evaluated using spatial distance metrics.\n\n")

            f.write("## Detailed Results\n\n")
            f.write("### Traditional Classification Metrics\n\n")
            f.write(df_summary[['Model', 'Accuracy', 'Precision', 'Recall', 'F1_Score', 'ROC_AUC']].to_markdown(index=False))
            f.write("\n\n")

            f.write("### Distance-Based Metrics\n\n")
            f.write(df_summary[['Model', 'Mean_Distance_Error', 'Median_Distance_Error', 'P90_Distance', 'P95_Distance']].to_markdown(index=False))
            f.write("\n\n")

            f.write("### Tolerance-Based Accuracy\n\n")
            f.write(df_summary[['Model', 'Within_15cm', 'Within_20cm', 'Within_25cm']].to_markdown(index=False))
            f.write("\n\n")

            f.write("## Architecture Analysis\n\n")
            arch_analysis = df_summary.groupby('Architecture').agg({
                'Mean_Distance_Error': ['mean', 'std'],
                'Accuracy': ['mean', 'std'],
                'Within_20cm': ['mean', 'std']
            }).round(4)
            f.write(arch_analysis.to_markdown())
            f.write("\n\n")

            f.write("## Temporal Window Analysis\n\n")
            temp_analysis = df_summary.groupby('Temporal_Window').agg({
                'Mean_Distance_Error': ['mean', 'std'],
                'Accuracy': ['mean', 'std'],
                'Within_20cm': ['mean', 'std']
            }).round(4)
            f.write(temp_analysis.to_markdown())
            f.write("\n\n")

            f.write("## Recommendations for Collaborative Robotics\n\n")
            f.write("1. **For High-Precision Applications (< 15cm tolerance):** Use models with lowest mean distance error\n\n")
            f.write("2. **For Standard Applications (< 20cm tolerance):** Balance between distance accuracy and computational efficiency\n\n")
            f.write("3. **For Robust Applications (< 25cm tolerance):** Consider models with highest tolerance-based accuracy\n\n")

        print(f"📄 Generated report: {report_file}")

        return df_summary


def main():
    """Main execution function for polygon-based distance evaluation."""
    print("🚀 Starting Polygon-Based Distance Evaluation System")
    print("=" * 80)

    # Initialize evaluator
    evaluator = PolygonDistanceEvaluator()

    # Run polygon distance evaluation on all models
    results = evaluator.evaluate_all_models_polygon_distance(max_samples=1000)

    if not results:
        print("❌ No successful evaluations!")
        return

    # Create visualizations
    evaluator.create_polygon_distance_visualizations(results)

    # Generate summary report
    summary_df = evaluator.generate_polygon_distance_report(results)

    print("\n" + "=" * 80)
    print("🎯 POLYGON DISTANCE EVALUATION COMPLETED SUCCESSFULLY!")
    print("=" * 80)
    print(f"📊 Models evaluated: {len(results)}")
    print(f"📁 Results saved to: {evaluator.results_dir}")
    print("\n📈 Top 3 Models by Distance Accuracy:")

    # Show top performers
    top_models = summary_df.nsmallest(3, 'Mean_Distance_Error')
    for idx, row in top_models.iterrows():
        print(f"   {idx+1}. {row['Model']}: {row['Mean_Distance_Error']:.4f}m mean error")

    print("\n📊 Top 3 Models by 20cm Tolerance Accuracy:")
    top_tolerance = summary_df.nlargest(3, 'Within_20cm')
    for idx, row in top_tolerance.iterrows():
        print(f"   {idx+1}. {row['Model']}: {row['Within_20cm']:.4f} accuracy within 20cm")

    print("\n✅ Check the output directory for detailed results and visualizations!")
    print("📄 Key files generated:")
    print(f"   - polygon_distance_evaluation_results.csv")
    print(f"   - POLYGON_DISTANCE_EVALUATION_REPORT.md")
    print(f"   - polygon_distance_comprehensive_comparison.png")


if __name__ == "__main__":
    main()
