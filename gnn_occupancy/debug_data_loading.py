#!/usr/bin/env python3
"""
Debug script to test data loading
"""

import os
from pathlib import Path
from data import OccupancyDataset
from torch_geometric.loader import Data<PERSON>oader

def test_data_loading():
    """Test data loading functionality."""
    print("🔍 Testing data loading...")
    
    data_dir = "/home/<USER>/ma_yugi/data/07_gnn_ready"
    
    # Check if directories exist
    print(f"📁 Data directory: {data_dir}")
    print(f"   Exists: {os.path.exists(data_dir)}")
    
    test_dir = os.path.join(data_dir, "test")
    print(f"📁 Test directory: {test_dir}")
    print(f"   Exists: {os.path.exists(test_dir)}")
    
    # Check temporal directories
    for temporal_window in [3, 5]:
        temporal_dir = os.path.join(test_dir, f"temporal_{temporal_window}")
        print(f"📁 Temporal {temporal_window} directory: {temporal_dir}")
        print(f"   Exists: {os.path.exists(temporal_dir)}")
        
        if os.path.exists(temporal_dir):
            files = [f for f in os.listdir(temporal_dir) if f.endswith('.pt')]
            print(f"   Files: {len(files)} .pt files")
            if files:
                print(f"   Sample files: {files[:3]}")
        
        # Try creating dataset
        try:
            dataset = OccupancyDataset(
                root=data_dir,
                split='test',
                temporal_window=temporal_window,
                binary_mapping={
                    'occupied': [1, 2, 3, 4],
                    'unoccupied': [0]
                }
            )
            print(f"   ✅ Dataset created: {len(dataset)} samples")
            
            # Try creating data loader
            data_loader = DataLoader(
                dataset,
                batch_size=1,
                shuffle=False,
                num_workers=0
            )
            print(f"   ✅ DataLoader created: {len(data_loader)} batches")
            
            # Try loading one batch
            if len(data_loader) > 0:
                batch = next(iter(data_loader))
                print(f"   ✅ Sample batch loaded:")
                print(f"      x shape: {batch.x.shape}")
                print(f"      y shape: {batch.y.shape}")
                print(f"      pos shape: {batch.pos.shape}")
                print(f"      edge_index shape: {batch.edge_index.shape}")
            else:
                print(f"   ⚠️ No batches in data loader")
                
        except Exception as e:
            print(f"   ❌ Error creating dataset: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_data_loading()
